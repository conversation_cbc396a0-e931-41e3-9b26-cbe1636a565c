#!/usr/bin/env python3
"""
JSON Validation Test Script
验证 gemini_2.5pro_25_08_27_10_prompt.json 文件是否符合预期格式
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set

def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 错误: JSON格式无效 - {e}")
        sys.exit(1)

def validate_json_structure(data: List[Dict[str, Any]]) -> List[str]:
    """验证JSON结构"""
    errors = []
    
    if not isinstance(data, list):
        errors.append("根元素应该是一个数组")
        return errors
    
    required_fields = {"id", "question", "type", "word_limit", "answer"}
    
    for i, item in enumerate(data):
        if not isinstance(item, dict):
            errors.append(f"第{i+1}个元素不是对象")
            continue
            
        # 检查必需字段
        missing_fields = required_fields - set(item.keys())
        if missing_fields:
            errors.append(f"第{i+1}个元素缺少字段: {', '.join(missing_fields)}")
        
        # 检查多余字段
        extra_fields = set(item.keys()) - required_fields
        if extra_fields:
            errors.append(f"第{i+1}个元素包含多余字段: {', '.join(extra_fields)}")
        
        # 检查字段类型
        if "id" in item and not isinstance(item["id"], str):
            errors.append(f"第{i+1}个元素的id字段应该是字符串")
        
        if "question" in item and not isinstance(item["question"], str):
            errors.append(f"第{i+1}个元素的question字段应该是字符串")
            
        if "type" in item and not isinstance(item["type"], str):
            errors.append(f"第{i+1}个元素的type字段应该是字符串")
            
        if "word_limit" in item and not isinstance(item["word_limit"], int):
            errors.append(f"第{i+1}个元素的word_limit字段应该是整数")
            
        if "answer" in item and not isinstance(item["answer"], str):
            errors.append(f"第{i+1}个元素的answer字段应该是字符串")
    
    return errors

def compare_with_template(template_data: List[Dict[str, Any]], 
                         answer_data: List[Dict[str, Any]]) -> List[str]:
    """与模板文件对比"""
    errors = []
    
    if len(template_data) != len(answer_data):
        errors.append(f"数据条目数量不匹配: 模板有{len(template_data)}条，回答有{len(answer_data)}条")
        return errors
    
    for i, (template_item, answer_item) in enumerate(zip(template_data, answer_data)):
        # 检查ID匹配
        if template_item.get("id") != answer_item.get("id"):
            errors.append(f"第{i+1}条记录ID不匹配: 期望'{template_item.get('id')}'，实际'{answer_item.get('id')}'")
        
        # 检查问题匹配
        if template_item.get("question") != answer_item.get("question"):
            errors.append(f"第{i+1}条记录问题不匹配: ID {template_item.get('id')}")
        
        # 检查类型匹配
        if template_item.get("type") != answer_item.get("type"):
            errors.append(f"第{i+1}条记录类型不匹配: ID {template_item.get('id')}, 期望'{template_item.get('type')}'，实际'{answer_item.get('type')}'")
        
        # 检查字数限制匹配
        if template_item.get("word_limit") != answer_item.get("word_limit"):
            errors.append(f"第{i+1}条记录字数限制不匹配: ID {template_item.get('id')}, 期望{template_item.get('word_limit')}，实际{answer_item.get('word_limit')}")
        
        # 检查答案是否为空
        answer_text = answer_item.get("answer", "")
        if not answer_text or answer_text.strip() == "":
            errors.append(f"第{i+1}条记录答案为空: ID {template_item.get('id')}")
    
    return errors

def count_words(text: str) -> int:
    """简单的单词计数（适用于英文）"""
    if not text:
        return 0
    # 移除多余空格并分割
    words = text.strip().split()
    return len(words)

def validate_word_limits(data: List[Dict[str, Any]]) -> List[str]:
    """验证答案字数是否符合限制"""
    errors = []
    warnings = []
    
    for i, item in enumerate(data):
        answer = item.get("answer", "")
        word_limit = item.get("word_limit", 0)
        word_count = count_words(answer)
        
        if word_count == 0:
            continue  # 空答案已在其他地方检查
        
        # 检查是否超出字数限制
        if word_count > word_limit:
            errors.append(f"第{i+1}条记录字数超限: ID {item.get('id')}, 限制{word_limit}字，实际{word_count}字")
        
        # 检查是否明显少于字数限制（少于80%可能需要注意）
        elif word_count < word_limit * 0.8:
            warnings.append(f"第{i+1}条记录字数偏少: ID {item.get('id')}, 限制{word_limit}字，实际{word_count}字 ({word_count/word_limit*100:.1f}%)")
    
    return errors, warnings

def main():
    """主函数"""
    print("🔍 开始验证JSON文件...")
    print("=" * 60)
    
    # 文件路径
    template_file = "data/preliminary_example_submission.json"

    answer_file = "results/singleresearch_with_before_0828_lastest.json"
    # answer_file = "results/gemini_2.5pro_25_08_27_10_prompt.json"
    

    # 加载文件
    print(f"📁 加载模板文件: {template_file}")
    template_data = load_json_file(template_file)
    
    print(f"📁 加载回答文件: {answer_file}")
    answer_data = load_json_file(answer_file)
    
    print(f"✅ 成功加载文件，模板有{len(template_data)}条记录，回答有{len(answer_data)}条记录")
    print()
    
    # 验证JSON结构
    print("🔧 验证JSON结构...")
    structure_errors = validate_json_structure(answer_data)
    if structure_errors:
        print("❌ JSON结构错误:")
        for error in structure_errors:
            print(f"   • {error}")
        print()
    else:
        print("✅ JSON结构正确")
        print()
    
    # 与模板对比
    print("🔍 与模板文件对比...")
    comparison_errors = compare_with_template(template_data, answer_data)
    if comparison_errors:
        print("❌ 与模板对比发现错误:")
        for error in comparison_errors:
            print(f"   • {error}")
        print()
    else:
        print("✅ 与模板文件完全匹配")
        print()
    
    # 验证字数限制
    print("📊 验证字数限制...")
    word_errors, word_warnings = validate_word_limits(answer_data)
    
    if word_errors:
        print("❌ 字数限制错误:")
        for error in word_errors:
            print(f"   • {error}")
        print()
    
    if word_warnings:
        print("⚠️  字数限制警告:")
        for warning in word_warnings:
            print(f"   • {warning}")
        print()
    
    if not word_errors and not word_warnings:
        print("✅ 所有答案字数都在合理范围内")
        print()
    
    # 统计信息
    print("📈 统计信息:")
    answered_count = sum(1 for item in answer_data if item.get("answer", "").strip())
    print(f"   • 总题目数: {len(answer_data)}")
    print(f"   • 已回答: {answered_count}")
    print(f"   • 未回答: {len(answer_data) - answered_count}")
    print(f"   • 完成率: {answered_count/len(answer_data)*100:.1f}%")
    
    # 按类型统计
    type_stats = {}
    for item in answer_data:
        item_type = item.get("type", "未知")
        if item_type not in type_stats:
            type_stats[item_type] = {"total": 0, "answered": 0}
        type_stats[item_type]["total"] += 1
        if item.get("answer", "").strip():
            type_stats[item_type]["answered"] += 1
    
    print("\n📊 按类型统计:")
    for item_type, stats in type_stats.items():
        completion_rate = stats["answered"] / stats["total"] * 100
        print(f"   • {item_type}: {stats['answered']}/{stats['total']} ({completion_rate:.1f}%)")
    
    # 总结
    print("\n" + "=" * 60)
    total_errors = len(structure_errors) + len(comparison_errors) + len(word_errors)
    
    if total_errors == 0:
        print("🎉 验证通过！JSON文件格式完全正确。")
    else:
        print(f"❌ 发现 {total_errors} 个错误，请修复后重新验证。")
        sys.exit(1)

if __name__ == "__main__":
    main()
