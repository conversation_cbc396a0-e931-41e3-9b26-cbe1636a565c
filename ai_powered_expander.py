import json
import sys
import shutil
import re
import requests
import time
from typing import List, Dict, Any

def count_words(text):
    """计算字数"""
    if not text:
        return 0
    return len(text.strip().split())

def analyze_content_structure(content: str) -> Dict[str, Any]:
    """分析内容结构，为扩展提供上下文"""
    paragraphs = content.split('\n\n')
    
    # 提取关键词和主题
    words = content.lower().split()
    
    # 检查现有结构
    has_introduction = len(paragraphs) > 0 and len(paragraphs[0]) > 100
    has_conclusion = any(keyword in content.lower() for keyword in 
                        ['conclusion', 'in summary', 'in conclusion', 'finally', 'overall'])
    has_examples = any(keyword in content.lower() for keyword in 
                      ['example', 'for instance', 'such as', 'case study'])
    
    return {
        'paragraph_count': len(paragraphs),
        'has_introduction': has_introduction,
        'has_conclusion': has_conclusion,
        'has_examples': has_examples,
        'avg_paragraph_length': sum(len(p.split()) for p in paragraphs) / len(paragraphs) if paragraphs else 0
    }

def generate_expansion_prompt(question: str, current_answer: str, topic_type: str,
                            words_needed: int, structure_info: Dict) -> str:
    """生成用于扩展内容的提示词"""

    # 根据缺少的内容结构，确定扩展方向
    expansion_focus = []

    if not structure_info['has_examples'] and words_needed > 100:
        expansion_focus.append("具体案例和实例")

    if not structure_info['has_conclusion'] and words_needed > 80:
        expansion_focus.append("总结和结论")

    if structure_info['paragraph_count'] < 3 and words_needed > 150:
        expansion_focus.append("深入分析和详细阐述")

    if words_needed > 200:
        expansion_focus.append("相关影响和未来展望")

    focus_text = "、".join(expansion_focus) if expansion_focus else "深入分析"

    prompt = f"""
请为以下学术问答扩展内容。这是一个英文学术问答，需要你生成高质量的英文扩展内容。

**原问题**: {question}

**当前回答**: {current_answer}

**扩展要求**:
- 需要增加约 {words_needed} 个英文单词
- 主题类型: {topic_type}
- 扩展重点: {focus_text}
- 保持学术性和专业性
- 与现有内容逻辑连贯
- 避免重复现有观点
- 使用正式的学术英语写作风格
- 提供具体的分析、案例或深入见解

**输出要求**:
- 只输出需要添加的英文扩展段落
- 每段之间用空行分隔
- 不要包含任何中文解释或元信息
- 确保内容原创且与主题高度相关

扩展内容:
"""

    return prompt

def generate_condensation_prompt(question: str, current_answer: str, topic_type: str,
                               words_to_reduce: int, structure_info: Dict) -> str:
    """生成用于精简内容的提示词"""

    # 计算当前字数和目标字数
    current_words = count_words(current_answer)
    target_words = current_words - words_to_reduce

    prompt = f"""
### 大师级提示词环境 (MPE) 2.0：内容精简专家

**核心身份 (Core Identity):** 你是一位**跨领域首席分析师**与**策略研究专家**，正在参加"SMP 2025 大模型报告生成挑战赛"。你的任务是对学术报告进行精准的内容精简，确保在满足字数要求的同时保持**逻辑深度、论证质量、观点洞察力**无可挑剔。

**最高目标 (Supreme Objective):** 将当前 {current_words} 个英文单词的回答精简到恰好 {target_words} 个英文单词，同时**最大化75%的大模型评审得分**。

**指导哲学 (Guiding Philosophy):** **分析为主，事实为辅 (Analysis First, Facts as Support)**。严格遵循 **"70/30 分析原则"**：精简后的报告中约70%的内容应为深刻的分析、阐释、推论和预测；剩余30%用于呈现核心数据、事实和引证。你的价值在于保留"**所以呢？(So What?)**"的深度分析。

---

**原问题**: {question}

**当前回答 ({current_words} 个英文单词)**: {current_answer}

---

### **精简执行协议 (Content Condensation Protocol)**

**第一步：内容结构分析**
识别当前回答的核心论点、支撑证据和分析深度，确定哪些内容属于核心分析（必须保留），哪些属于冗余表述（可以精简）。

**第二步：智能精简策略**
- 保留所有核心论点和关键洞察
- 合并重复或相似的观点表述
- 简化复杂句式，但保持学术严谨性
- 删除过度修饰词，但保留专业术语
- 确保 I-C-E 论证结构完整（引入-引用-解释）

**第三步：字数精确控制**
最终输出必须恰好控制在 {target_words} 个英文单词（允许误差±10个单词）。

---

### **输出要求**
- 输出完整的精简后英文回答
- 严格控制在 {target_words} 个英文单词
- 保持原有逻辑结构和学术深度
- 确保所有核心分析和洞察得以保留
- 不包含任何中文解释或元信息

**关键提醒**: 你的目标是 {target_words} 个英文单词，不是更多也不是更少。

精简后的回答:
"""

    return prompt

def call_qwen_api_for_expansion(prompt: str, api_key: str, max_retries: int = 3) -> str:
    """调用Qwen API生成扩展内容"""

    url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "qwen-plus",  # 或者 "qwen-max", "qwen-turbo"
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        },
        "parameters": {
            "max_tokens": 600,
            "temperature": 0.7,
            "top_p": 0.8
        }
    }

    for attempt in range(max_retries):
        try:
            print(f"  正在调用Qwen API生成扩展内容... (尝试 {attempt + 1}/{max_retries})")

            response = requests.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'output' in result and 'text' in result['output']:
                    return result['output']['text'].strip()
                else:
                    print(f"  API响应格式异常: {result}")

            else:
                print(f"  API调用失败，状态码: {response.status_code}")
                print(f"  响应内容: {response.text}")

        except Exception as e:
            print(f"  API调用异常 (尝试 {attempt + 1}): {e}")

        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # 指数退避

    print("  所有API调用尝试都失败，使用备用方案")
    return generate_fallback_expansion(prompt)

def call_qwen_api_for_condensation(prompt: str, api_key: str, max_retries: int = 3) -> str:
    """调用Qwen API生成精简内容"""

    url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "qwen-plus",  # 或者 "qwen-max", "qwen-turbo"
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        },
        "parameters": {
            "max_tokens": 1500,  # 增加最大token数，确保能生成完整的精简内容
            "temperature": 0.2,  # 精简时使用更低的温度，保持一致性和精确性
            "top_p": 0.8
        }
    }

    for attempt in range(max_retries):
        try:
            print(f"  正在调用Qwen API生成精简内容... (尝试 {attempt + 1}/{max_retries})")

            response = requests.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'output' in result and 'text' in result['output']:
                    return result['output']['text'].strip()
                else:
                    print(f"  API响应格式异常: {result}")

            else:
                print(f"  API调用失败，状态码: {response.status_code}")
                print(f"  响应内容: {response.text}")

        except Exception as e:
            print(f"  API调用异常 (尝试 {attempt + 1}): {e}")

        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # 指数退避

    print("  所有API调用尝试都失败，使用备用方案")
    return generate_fallback_condensation(prompt)

def generate_mock_expansion(prompt: str) -> str:
    """模拟大模型生成的扩展内容（仅用于演示）"""
    # 这是模拟函数，实际使用时会被真实的API调用替换

    if "AI" in prompt or "Tech" in prompt:
        return """The implementation of these technological solutions requires careful consideration of ethical frameworks and responsible development practices. Organizations must establish clear guidelines for data governance, algorithmic transparency, and user privacy protection while ensuring that technological advancement serves broader societal interests.

Furthermore, the scalability and adaptability of these systems depend on robust architectural design and continuous monitoring mechanisms. Performance optimization, security protocols, and user experience considerations must be balanced to create solutions that are both technically sound and practically viable for widespread adoption."""

    elif "Health" in prompt:
        return """Clinical validation and regulatory approval processes ensure that these interventions meet rigorous safety and efficacy standards. Comprehensive clinical trials, post-market surveillance, and ongoing safety monitoring are essential components of responsible healthcare innovation that prioritizes patient welfare above all other considerations.

The integration of these approaches into existing healthcare systems requires careful planning and stakeholder coordination. Training programs for healthcare professionals, patient education initiatives, and infrastructure development must be implemented systematically to ensure successful adoption and optimal patient outcomes."""

    else:
        return """Comprehensive evaluation frameworks are essential for measuring the effectiveness and impact of these approaches across different contexts and populations. Standardized metrics, longitudinal studies, and comparative analysis provide the evidence base necessary for informed decision-making and continuous improvement.

The sustainability and long-term viability of these initiatives depend on robust funding mechanisms, institutional support, and community engagement. Strategic partnerships, resource allocation, and adaptive management approaches are crucial for maintaining momentum and achieving lasting positive outcomes."""

def generate_mock_condensation(current_answer: str) -> str:
    """模拟大模型生成的精简内容（仅用于演示）"""
    # 简单的精简策略：移除一些修饰词和冗余表述
    condensed = current_answer

    # 移除一些常见的冗余表述
    redundant_phrases = [
        "it is important to note that",
        "it should be emphasized that",
        "furthermore, it is worth mentioning that",
        "additionally, it is crucial to understand that",
        "moreover, we must consider that",
        "in this context, it is essential to recognize that"
    ]

    for phrase in redundant_phrases:
        condensed = condensed.replace(phrase, "")

    # 简化一些表述
    condensed = condensed.replace("comprehensive and detailed", "comprehensive")
    condensed = condensed.replace("significant and important", "significant")
    condensed = condensed.replace("various different", "various")

    # 移除多余的空格和换行
    condensed = re.sub(r'\s+', ' ', condensed)
    condensed = re.sub(r'\n\s*\n', '\n\n', condensed)

    return condensed.strip()

def generate_fallback_expansion(prompt: str) -> str:
    """当API调用失败时的备用扩展方案"""
    return """Additional considerations include the need for comprehensive evaluation frameworks, stakeholder engagement strategies, and adaptive implementation approaches that can respond to changing circumstances and emerging challenges. Success in this domain requires careful balance between innovation and practical constraints while maintaining focus on sustainable long-term outcomes.

The broader implications of these developments extend beyond immediate applications to encompass systemic changes in how we approach complex problems and develop effective solutions. Understanding these wider impacts is essential for informed decision-making and strategic planning in an increasingly interconnected and rapidly evolving environment."""

def generate_fallback_condensation(prompt: str) -> str:
    """当API调用失败时的备用精简方案"""
    # 从prompt中提取原始答案进行简单精简
    lines = prompt.split('\n')
    current_answer = ""

    # 查找当前回答部分
    for i, line in enumerate(lines):
        if "**当前回答**:" in line and i + 1 < len(lines):
            # 提取当前回答内容
            answer_start = i + 2
            for j in range(answer_start, len(lines)):
                if lines[j].startswith("**") and "要求" in lines[j]:
                    break
                current_answer += lines[j] + "\n"
            break

    if current_answer:
        return generate_mock_condensation(current_answer)
    else:
        # 如果无法提取，返回一个通用的精简版本
        return "The key findings demonstrate significant implications for practical implementation and future development in this domain."

def ai_powered_adjust_content(file_path: str, use_real_api: bool = False, max_items: int = None, api_key: str = None):
    """
    使用AI动态调整内容（扩展或精简）

    Args:
        file_path: JSON文件路径
        use_real_api: 是否使用真实的Qwen API（需要配置API key）
        max_items: 最大处理条目数（None表示处理全部）
        api_key: Qwen API密钥
    """
    print(f"正在使用AI动态调整内容...")
    print(f"API模式: {'Qwen API' if use_real_api else '模拟模式'}")

    if use_real_api and not api_key:
        print("错误: 使用真实API需要提供api_key参数")
        return 0

    # 备份文件
    backup_file = file_path + '.ai_backup'
    shutil.copy2(file_path, backup_file)
    print(f"已创建备份文件: {backup_file}")

    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 识别需要调整的条目
    items_to_adjust = []
    for i, item in enumerate(data):
        if item['answer'].strip():
            actual_words = count_words(item['answer'])
            required_words = item['word_limit']
            deviation_percent = ((actual_words - required_words) / required_words) * 100

            # 字数偏差超过15%的条目需要调整
            if abs(deviation_percent) > 15:
                adjustment_type = "expand" if deviation_percent < 0 else "condense"
                words_difference = abs(actual_words - required_words)

                items_to_adjust.append({
                    'index': i,
                    'id': item['id'],
                    'deviation_percent': deviation_percent,
                    'actual_words': actual_words,
                    'required_words': required_words,
                    'words_difference': words_difference,
                    'adjustment_type': adjustment_type
                })

    # 按偏差程度排序，优先处理偏差最大的
    items_to_adjust.sort(key=lambda x: abs(x['deviation_percent']), reverse=True)

    expand_count = sum(1 for item in items_to_adjust if item['adjustment_type'] == 'expand')
    condense_count = sum(1 for item in items_to_adjust if item['adjustment_type'] == 'condense')

    print(f"发现 {len(items_to_adjust)} 个需要调整的条目:")
    print(f"  - 需要扩展: {expand_count} 个")
    print(f"  - 需要精简: {condense_count} 个")

    # 确定处理数量
    if max_items is None:
        items_to_process = items_to_adjust
        print(f"将处理全部 {len(items_to_adjust)} 个条目")

        # 如果使用真实API且条目数量较多，给出提醒
        if use_real_api and len(items_to_adjust) > 20:
            estimated_cost = len(items_to_adjust) * 0.01  # 粗略估算成本
            print(f"⚠️  注意: 将调用 {len(items_to_adjust)} 次API，预估成本约 ${estimated_cost:.2f}")
            confirm = input("是否继续？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                print("操作已取消")
                return 0
    else:
        items_to_process = items_to_adjust[:max_items]
        if len(items_to_adjust) > max_items:
            print(f"限制处理前 {max_items} 个最需要调整的条目")
    
    # 调整条目
    adjusted_count = 0
    for item_info in items_to_process:
        index = item_info['index']
        item = data[index]
        adjustment_type = item_info['adjustment_type']

        if adjustment_type == 'expand':
            print(f"\n正在扩展 {item['id']} (不足 {abs(item_info['deviation_percent']):.1f}%, 需要 {item_info['words_difference']} 字)")

            # 分析当前内容结构
            structure_info = analyze_content_structure(item['answer'])

            # 生成扩展提示词
            prompt = generate_expansion_prompt(
                item['question'],
                item['answer'],
                item['type'],
                item_info['words_difference'],
                structure_info
            )

            # 调用AI生成扩展内容
            if use_real_api:
                expansion_text = call_qwen_api_for_expansion(prompt, api_key)
            else:
                expansion_text = generate_mock_expansion(prompt)

            if expansion_text:
                # 将扩展内容添加到原答案中
                expanded_answer = item['answer'] + "\n\n" + expansion_text
                data[index]['answer'] = expanded_answer

                # 验证扩展效果
                new_word_count = count_words(expanded_answer)
                new_deviation = ((new_word_count - item['word_limit']) / item['word_limit']) * 100
                print(f"  扩展后: {new_word_count}/{item['word_limit']} 字 (偏差 {new_deviation:.1f}%)")

                adjusted_count += 1
            else:
                print(f"  扩展失败，跳过此条目")

        elif adjustment_type == 'condense':
            print(f"\n正在精简 {item['id']} (超出 {abs(item_info['deviation_percent']):.1f}%, 需要减少 {item_info['words_difference']} 字)")

            # 分析当前内容结构
            structure_info = analyze_content_structure(item['answer'])

            # 生成精简提示词
            prompt = generate_condensation_prompt(
                item['question'],
                item['answer'],
                item['type'],
                item_info['words_difference'],
                structure_info
            )

            # 调用AI生成精简内容
            if use_real_api:
                condensed_text = call_qwen_api_for_condensation(prompt, api_key)
            else:
                condensed_text = generate_mock_condensation(item['answer'])

            if condensed_text:
                # 替换为精简后的内容
                data[index]['answer'] = condensed_text

                # 验证精简效果
                new_word_count = count_words(condensed_text)
                new_deviation = ((new_word_count - item['word_limit']) / item['word_limit']) * 100
                print(f"  精简后: {new_word_count}/{item['word_limit']} 字 (偏差 {new_deviation:.1f}%)")

                adjusted_count += 1
            else:
                print(f"  精简失败，跳过此条目")

        # 显示进度
        if adjusted_count % 10 == 0:
            print(f"  📊 进度: 已处理 {adjusted_count}/{len(items_to_process)} 个条目")
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    print(f"\nAI动态调整完成! 共调整了 {adjusted_count} 个条目")
    return adjusted_count

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python ai_powered_expander.py <json_file> [--real-api] [--max-items N] [--api-key KEY]")
        print("  --real-api: 使用真实的Qwen API")
        print("  --max-items N: 最大处理条目数（默认处理全部）")
        print("  --api-key KEY: Qwen API密钥（也可通过环境变量 QWEN_API_KEY 设置）")
        sys.exit(1)
    
    file_path = sys.argv[1]
    use_real_api = '--real-api' in sys.argv
    
    max_items = None  # 默认处理全部
    if '--max-items' in sys.argv:
        try:
            max_items_index = sys.argv.index('--max-items') + 1
            max_items = int(sys.argv[max_items_index])
            print(f"设置最大处理条目数: {max_items}")
        except (ValueError, IndexError):
            print("警告: --max-items 参数无效，将处理全部条目")
            max_items = None
    
    # 获取API key
    api_key = None
    if use_real_api:
        if '--api-key' in sys.argv:
            try:
                api_key_index = sys.argv.index('--api-key') + 1
                api_key = sys.argv[api_key_index]
            except IndexError:
                print("错误: --api-key 参数需要提供API密钥值")
                sys.exit(1)
        else:
            # 尝试从环境变量获取
            import os
            api_key = os.getenv('QWEN_API_KEY')
            if not api_key:
                print("错误: 使用真实API需要通过 --api-key 参数或 QWEN_API_KEY 环境变量提供API密钥")
                sys.exit(1)

    adjusted_count = ai_powered_adjust_content(file_path, use_real_api, max_items, api_key)
